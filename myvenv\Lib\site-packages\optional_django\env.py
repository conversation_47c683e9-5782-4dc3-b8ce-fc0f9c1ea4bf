DJANGO_INSTALLED = False
DJANGO_CONFIGURED = False
DJANGO_SETTINGS = None

try:
    import django
    DJANGO_INSTALLED = True
except ImportError:
    pass

if DJ<PERSON><PERSON><PERSON>_INSTALLED:
    try:
        from django.core.exceptions import ImproperlyConfigured
    except ImportError:
        DJ<PERSON><PERSON><PERSON>_INSTALLED = False
    if DJ<PERSON><PERSON>O_INSTALLED:
        try:
            from django.conf import settings as DJ<PERSON><PERSON><PERSON>_SETTINGS
            # Try and raise an ImproperlyConfigured error
            getattr(DJANGO_SETTINGS, 'DEBUG', None)
            DJANGO_CONFIGURED = True
        except ImproperlyConfigured:
            DJANGO_SETTINGS = None