"""Database models and session management."""
import os
import logging
from typing import Optional, Dict, Any, Generator
from sqlmodel import SQLModel, Session, text
from contextlib import contextmanager

# Import the configured engine from db_config
from db_config import engine, DATABASE_URL

# Get logger
logger = logging.getLogger('app.database')

# Ensure the database directory exists for SQLite
if 'sqlite' in DATABASE_URL and not DATABASE_URL.startswith('sqlite:///:memory:'):
    db_path = DATABASE_URL.split('sqlite:///')[-1]
    db_dir = os.path.dirname(db_path)
    if db_dir and not os.path.exists(db_dir):
        logger.info(f"Creating database directory: {db_dir}")
        os.makedirs(db_dir, exist_ok=True)

@contextmanager
def get_db_session() -> Generator[Session, None, None]:
    """Get a database session with proper cleanup.
    
    Yields:
        Session: A database session
        
    Example:
        with get_db_session() as session:
            result = session.execute(text("SELECT 1"))
            return result.scalar()
    """
    session = Session(engine)
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        logger.error(f"Database error: {str(e)}", exc_info=True)
        raise
    finally:
        session.close()

async def init_db():
    """Initialize database and create tables with indexes."""
    logger.info(f"Initializing database at: {DATABASE_URL}")
    
    try:
        # Create all tables and verify indexes in a single transaction
        with get_db_session() as session:
            # Create tables
            logger.info("Creating database tables...")
            SQLModel.metadata.create_all(bind=engine)
            logger.info("Database tables created successfully")
            
            # Verify and create indexes if needed
            if 'sqlite' in DATABASE_URL:
                await verify_sqlite_indexes(session)
            elif 'postgresql' in DATABASE_URL:
                await verify_postgres_indexes(session)
            else:
                logger.warning("Index verification not implemented for this database type")
                
    except Exception as e:
        logger.error(f"Database initialization failed: {str(e)}")
        if os.getenv('DEBUG', 'false').lower() == 'true':
            logger.exception("Database initialization error details:")
        raise

async def verify_sqlite_indexes(session: Session):
    """Verify SQLite indexes."""
    try:
        result = session.execute(text(
            "SELECT name FROM sqlite_master "
            "WHERE type='index' AND tbl_name='ip_records'"
        ))
        indexes = {row[0] for row in result}
        
        expected_indexes = {
            'ix_ip_records_ip_cidr',
            'ix_ip_records_country',
            'ix_ip_records_owner',
            'ix_ip_records_added_at',
            'idx_ip_range'
        }
        
        missing_indexes = expected_indexes - indexes
        if missing_indexes:
            logger.warning(f"Missing indexes: {missing_indexes}")
            # Create missing indexes
            for idx in missing_indexes:
                try:
                    if idx == 'idx_ip_range':
                        session.execute(text(
                            "CREATE INDEX idx_ip_range ON ip_records (ip_start, ip_end)"
                        ))
                    else:
                        col = idx.replace('ix_ip_records_', '')
                        session.execute(text(
                            f"CREATE INDEX {idx} ON ip_records ({col})"
                        ))
                    session.commit()
                    logger.info(f"Created missing index: {idx}")
                except Exception as idx_error:
                    logger.error(f"Failed to create index {idx}: {str(idx_error)}")
                    session.rollback()
        else:
            logger.info("All expected indexes exist")
            
    except Exception as e:
        logger.error(f"Error verifying SQLite indexes: {str(e)}")
        raise

async def verify_postgres_indexes(session: Session):
    """Verify PostgreSQL indexes."""
    try:
        # Add PostgreSQL specific index verification if needed
        logger.info("PostgreSQL index verification not yet implemented")
    except Exception as e:
        logger.error(f"Error verifying PostgreSQL indexes: {str(e)}")
        raise

def get_session() -> Session:
    """Get a database session."""
    return Session(engine)

def check_database_connection() -> bool:
    """Check if database is accessible with detailed error reporting."""
    logger.info("Checking database connection...")
    
    # Check if database file exists for SQLite
    if 'sqlite' in DATABASE_URL and not DATABASE_URL.startswith('sqlite:///:memory:'):
        db_path = DATABASE_URL.split('sqlite:///')[-1]
        if not os.path.exists(db_path):
            logger.warning(f"SQLite database file does not exist: {db_path}")
            # Try to create the database file by initializing it
            try:
                SQLModel.metadata.create_all(engine)
                logger.info(f"Created new SQLite database at: {db_path}")
            except Exception as create_error:
                logger.error(f"Failed to create SQLite database: {str(create_error)}")
                return False
    
    # Test the connection
    try:
        with get_db_session() as session:
            # Test basic query
            result = session.execute(text("SELECT 1"))
            if not result.scalar() == 1:
                logger.error("Unexpected result from test query")
                return False
            
            # Check if tables exist
            if 'sqlite' in DATABASE_URL:
                result = session.execute(text(
                    "SELECT name FROM sqlite_master WHERE type='table'"
                ))
                tables = [row[0] for row in result]
                logger.info(f"Found {len(tables)} tables in database")
                if not tables:
                    logger.warning("No tables found in database")
            
            logger.info("Database connection test passed")
            return True
            
    except Exception as e:
        logger.error(f"Database connection test failed: {str(e)}")
        if os.getenv('DEBUG', 'false').lower() == 'true':
            logger.exception("Connection error details:")
        return False
