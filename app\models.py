from sqlmodel import SQLModel, Field, Column, Integer, String, Boolean, DateTime, Index, func, Relationship
from typing import Optional, List, TYPE_CHECKING
from datetime import datetime, timedelta
from pydantic import BaseModel, EmailStr, validator
from passlib.context import CryptContext

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

class UserBase(SQLModel):
    username: str = Field(sa_column=Column(String, unique=True, index=True))
    email: str = Field(sa_column=Column(String, unique=True, index=True))
    full_name: Optional[str] = None
    is_active: bool = Field(default=True)
    is_superuser: bool = Field(default=False)

class User(UserBase, table=True):
    __tablename__ = "users"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    hashed_password: str
    created_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), server_default=func.now())
    )
    last_login: Optional[datetime] = None
    failed_login_attempts: int = Field(default=0)
    account_locked_until: Optional[datetime] = None
    
    # Relationships
    if TYPE_CHECKING:
        sessions: List["UserSession"]
    else:
        sessions: List["UserSession"] = Relationship(back_populates="user")

class UserCreate(BaseModel):
    username: str
    email: EmailStr
    password: str
    full_name: Optional[str] = None
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one number')
        return v

class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None
    password: Optional[str] = None

class UserInDB(UserBase):
    id: int
    created_at: datetime
    last_login: Optional[datetime] = None
    
    class Config:
        orm_mode = True

class UserSession(SQLModel, table=True):
    __tablename__ = "user_sessions"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="users.id")
    session_token: str = Field(index=True)
    refresh_token: str = Field(index=True)
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None
    expires_at: datetime
    created_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), server_default=func.now())
    )
    last_activity: datetime = Field(
        sa_column=Column(DateTime(timezone=True), onupdate=func.now())
    )
    
    # Relationships
    if TYPE_CHECKING:
        user: "User"
    else:
        user: "User" = Relationship(back_populates="sessions")

class IPRecord(SQLModel, table=True):
    __tablename__ = "ip_records"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    ip_cidr: str = Field(index=True)
    ip_version: int
    ip_start: int = Field(sa_column=Column(Integer, index=True))
    ip_end: int = Field(sa_column=Column(Integer, index=True))
    country: str = Field(index=True)
    owner: str = Field(index=True)
    added_at: datetime = Field(default_factory=datetime.utcnow, index=True)
    comment: Optional[str] = None
    
    __table_args__ = (
        Index('idx_ip_range', 'ip_start', 'ip_end'),
    )

class AuditLog(SQLModel, table=True):
    __tablename__ = "audit_logs"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    action: str
    admin_user: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    details: Optional[str] = None
