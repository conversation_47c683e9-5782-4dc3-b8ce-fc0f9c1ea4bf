"""Security middleware and utilities for the application."""
from fastapi import Request, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from slowapi import Limiter
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from functools import wraps
import os
from typing import List, Optional, Dict, Any, Callable, Union
import re
from datetime import datetime, timedelta
import logging
import asyncio

# Configure logging
logger = logging.getLogger(__name__)

# Initialize rate limiter
limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["1000 per day", "100 per hour"],
    storage_uri="memory://",
    headers_enabled=True
)

# Rate limit configurations
RATE_LIMITS = {
    "/api/v1/lookup/": ["10/minute", "100/hour"],
    "/api/v1/auth/login/": ["5/minute", "50/hour"],
    "/api/v1/auth/refresh/": ["10/minute", "100/hour"],
    "/api/v1/admin/": ["100/hour", "1000/day"],
}

# Security headers configuration
SECURITY_HEADERS = {
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "X-XSS-Protection": "1; mode=block",
    "Content-Security-Policy": "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline';",
    "Referrer-Policy": "strict-origin-when-cross-origin",
    "Permissions-Policy": "geolocation=(), microphone=(), camera=()",
}

def get_cors_middleware(allowed_origins: List[str] = None):
    """Create and return CORS middleware with secure defaults."""
    if allowed_origins is None:
        allowed_origins = ["*"]  # In production, replace with specific origins
    
    return CORSMiddleware(
        allow_origins=allowed_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
        expose_headers=["Content-Length", "X-Request-ID"],
        max_age=600,  # 10 minutes
    )

def add_security_headers(request: Request, response):
    """Add security headers to the response."""
    for header, value in SECURITY_HEADERS.items():
        response.headers[header] = value
    return response

def get_rate_limit_for_path(path: str) -> list:
    """Get rate limit for a specific path."""
    for route, limits in RATE_LIMITS.items():
        if path.startswith(route):
            return limits
    return ["200/day"]  # Default rate limit

def validate_input(input_str: str, max_length: int = 255, pattern: str = None) -> bool:
    """Validate input string against length and optional regex pattern."""
    if not input_str or len(input_str) > max_length:
        return False
    if pattern and not re.match(pattern, input_str):
        return False
    return True

def sanitize_input(input_str: str) -> str:
    """Basic HTML/JS input sanitization."""
    if not input_str:
        return ""
    
    # Remove or escape potentially dangerous characters
    sanitized = input_str.replace("<", "&lt;").replace(">", "&gt;")
    sanitized = sanitized.replace("'", "&#x27;").replace('"', "&quot;")
    sanitized = sanitized.replace("(", "&#40;").replace(")", "&#41;")
    
    return sanitized

class SecurityMiddleware:
    """Custom security middleware for additional protection."""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            return await self.app(scope, receive, send)

        # Create a modified send function to add headers
        async def send_with_headers(message):
            if message["type"] == "http.response.start":
                # Add security headers
                headers = dict(message.get("headers", []))
                for name, value in SECURITY_HEADERS.items():
                    name_bytes = name.encode("latin-1")
                    value_bytes = value.encode("latin-1")
                    headers[name_bytes] = value_bytes
                
                # Convert headers back to list of tuples
                message["headers"] = [(k, v) for k, v in headers.items()]
                
            await send(message)

        # Process the request
        try:
            await self.app(scope, receive, send_with_headers)
        except Exception as e:
            logger.error(f"Error in security middleware: {str(e)}")
            raise
    
    def _check_suspicious_headers(self, request: Request):
        """Check for suspicious request headers."""
        suspicious_headers = [
            'x-forwarded-for',
            'x-forwarded-host',
            'x-forwarded-proto',
            'x-real-ip',
        ]
        
        for header in suspicious_headers:
            if header in request.headers:
                logger.warning(
                    "Suspicious header detected: %s: %s from %s",
                    header,
                    request.headers[header],
                    request.client.host if request.client else "unknown"
                )

def get_rate_limiter():
    return limiter

# Initialize rate limiter
rate_limiter = limiter

def limit(limit_value: str, key_func: Callable = None):
    """Decorator to apply rate limiting to a route."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Error in rate-limited function: {str(e)}")
                raise
        return wrapper
    return decorator
