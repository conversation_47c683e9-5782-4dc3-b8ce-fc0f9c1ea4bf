<!DOCTYPE html>
<html lang="en" class="h-full bg-gray-900">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - IP Lookup Service</title>
    <link rel="stylesheet" href="/static/css/tailwind-essentials.css">
    <link rel="stylesheet" href="/static/style.css">
</head>
<body class="h-full flex flex-col">
    <!-- Navigation -->
    <nav class="bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <a href="/" class="text-white font-bold">IP Lookup</a>
                    </div>
                    <div class="hidden md:block">
                        <div class="ml-10 flex items-baseline space-x-4">
                            <a href="/" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">Home</a>
                        </div>
                    </div>
                </div>
                <div class="hidden md:block">
                    <div class="ml-4 flex items-center md:ml-6">
                        <a href="/login" class="bg-gray-900 text-white px-3 py-2 rounded-md text-sm font-medium">Login</a>
                        <a href="/signup" class="ml-4 text-gray-300 hover:bg-gray-700 hover:text-white px-4 py-2 rounded-md text-sm font-medium">Sign Up</a>
                    </div>
                </div>
                <!-- Mobile menu button -->
                <div class="-mr-2 flex md:hidden">
                    <button type="button" id="mobile-menu-button" class="bg-gray-800 inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white">
                        <span class="sr-only">Open main menu</span>
                        <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu, show/hide based on menu state. -->
        <div class="md:hidden hidden" id="mobile-menu">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                <a href="/" class="text-gray-300 hover:bg-gray-700 hover:text-white block px-3 py-2 rounded-md text-base font-medium">Home</a>
                <a href="/login" class="bg-gray-900 text-white block px-3 py-2 rounded-md text-base font-medium">Login</a>
                <a href="/signup" class="text-gray-300 hover:bg-gray-700 hover:text-white block px-3 py-2 rounded-md text-base font-medium">Sign Up</a>
            </div>
        </div>
    </nav>

    <!-- Main content -->
    <main class="flex-grow flex items-center justify-center px-4 py-12 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8 bg-gray-800 p-8 rounded-lg shadow-lg">
            <div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-white">Sign in to your account</h2>
                <p class="mt-2 text-center text-sm text-gray-400">
                    Or
                    <a href="/signup.html" class="font-medium text-blue-400 hover:text-blue-300">create a new account</a>
                </p>
            </div>
            <form id="login-form" class="mt-8 space-y-6">
                <div id="login-error" class="hidden bg-red-500/20 border-l-4 border-red-500 text-red-100 p-4 mb-4 rounded">
                    <p id="error-message"></p>
                </div>
                <div class="rounded-md shadow-sm space-y-4">
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-300 mb-1">Username or Email</label>
                        <input id="username" name="username" type="text" required 
                               class="appearance-none relative block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="Enter your username or email">
                    </div>
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-300 mb-1">Password</label>
                        <input id="password" name="password" type="password" autocomplete="current-password" required 
                               class="appearance-none relative block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="Enter your password">
                        <div class="flex items-center justify-between mt-2">
                            <div class="flex items-center">
                                <input id="remember-me" name="remember_me" type="checkbox" 
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-600 rounded bg-gray-700">
                                <label for="remember-me" class="ml-2 block text-sm text-gray-300">
                                    Remember me
                                </label>
                            </div>
                            <div class="text-sm">
                                <a href="#" class="font-medium text-blue-400 hover:text-blue-300">
                                    Forgot your password?
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <button type="submit" id="login-button"
                            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <svg class="h-5 w-5 text-blue-500 group-hover:text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                            </svg>
                        </span>
                        Sign in
                    </button>
                </div>
            </form>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <p class="text-center text-gray-400 text-sm">
                &copy; 2025 IP Lookup Service. All rights reserved.
            </p>
        </div>
    </footer>

    <!-- Toast notification -->
    <div id="toast" class="fixed bottom-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg transform translate-y-16 opacity-0 transition-all duration-300 ease-in-out">
        <div class="flex items-center">
            <span id="toast-message">Success! You have been logged in.</span>
            <button id="toast-close" class="ml-4 text-white hover:text-gray-200">
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
    </div>

    <script>
        // Mobile menu toggle
        document.addEventListener('DOMContentLoaded', () => {
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            
            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', () => {
                    mobileMenu.classList.toggle('hidden');
                });
            }

            // Login form submission
            const loginForm = document.getElementById('login-form');
            const loginButton = document.getElementById('login-button');
            const errorDiv = document.getElementById('login-error');
            const errorMessage = document.getElementById('error-message');
            const toast = document.getElementById('toast');
            const toastMessage = document.getElementById('toast-message');
            const toastClose = document.getElementById('toast-close');

            if (loginForm) {
                loginForm.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    
                    // Reset error state
                    errorDiv.classList.add('hidden');
                    
                    // Get form data
                    const formData = {
                        username: document.getElementById('username').value.trim(),
                        password: document.getElementById('password').value,
                        remember_me: document.getElementById('remember-me').checked
                    };
                    
                    // Disable button and show loading state
                    const originalButtonText = loginButton.innerHTML;
                    loginButton.disabled = true;
                    loginButton.innerHTML = `
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Signing in...
                    `;
                    
                    try {
                        // Send login request
                        const response = await fetch('/api/v1/auth/login/', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: new URLSearchParams({
                                'username': formData.username,
                                'password': formData.password
                            })
                        });
                        
                        const data = await response.json();
                        
                        if (!response.ok) {
                            throw new Error(data.detail || 'Login failed');
                        }
                        
                        // Store tokens
                        if (data.access_token) {
                            localStorage.setItem('access_token', data.access_token);
                            if (data.refresh_token) {
                                localStorage.setItem('refresh_token', data.refresh_token);
                            }
                            
                            // Show success message and redirect
                            showToast('Login successful! Redirecting...');
                            
                            // Redirect to admin dashboard after a short delay
                            setTimeout(() => {
                                window.location.href = '/admin.html';
                            }, 1000);
                        }
                        
                    } catch (error) {
                        console.error('Login error:', error);
                        showError(error.message || 'Login failed. Please check your credentials and try again.');
                    } finally {
                        // Reset button state
                        loginButton.disabled = false;
                        loginButton.innerHTML = originalButtonText;
                    }
                });
            }
            
            function showError(message) {
                errorMessage.textContent = message;
                errorDiv.classList.remove('hidden');
                errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
            
            function showToast(message) {
                toastMessage.textContent = message;
                toast.classList.remove('translate-y-16', 'opacity-0');
                toast.classList.add('translate-y-0', 'opacity-100');
                
                // Auto-hide after 5 seconds
                setTimeout(() => {
                    toast.classList.remove('translate-y-0', 'opacity-100');
                    toast.classList.add('translate-y-16', 'opacity-0');
                }, 5000);
            }
            
            // Close toast when X is clicked
            if (toastClose) {
                toastClose.addEventListener('click', () => {
                    toast.classList.remove('translate-y-0', 'opacity-100');
                    toast.classList.add('translate-y-16', 'opacity-0');
                });
            }
        });
    </script>
</body>
</html>
