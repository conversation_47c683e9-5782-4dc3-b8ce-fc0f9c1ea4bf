"""Application configuration settings."""
import os
from pathlib import Path
from pydantic import BaseModel, HttpUrl, validator, Field
from typing import List, Optional, Dict, Any, Union
from dotenv import load_dotenv

# Get the project root directory (one level up from app/)
project_root = Path(__file__).parent.parent
env_path = project_root / '.env'

# Load environment variables from .env file if it exists
load_dotenv(env_path, override=True)

# Debug: Print environment variables and .env path
print(f"Loading .env from: {env_path}")
print(f"Current working directory: {os.getcwd()}")
print(f"Environment variables loaded: {os.environ.get('DATABASE_URL', 'Not found')}")

class Settings(BaseModel):
    # Application settings
    APP_NAME: str = "IP Lookup Service"
    DEBUG: bool = Field(default_factory=lambda: os.getenv("DEBUG", "false").lower() == "true")
    ENVIRONMENT: str = Field(default_factory=lambda: os.getenv("ENVIRONMENT", "production"))
    SECRET_KEY: str = Field(default_factory=lambda: os.getenv("SECRET_KEY", "your-secret-key-here"))
    
    # API settings
    API_V1_STR: str = "/api/v1"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default_factory=lambda: int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30")))
    REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default_factory=lambda: int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", "7")))
    
    # CORS settings
    BACKEND_CORS_ORIGINS: List[str] = Field(
        default_factory=lambda: os.getenv(
            "BACKEND_CORS_ORIGINS", 
            "http://localhost:8000,http://127.0.0.1:8000"
        ).split(",")
    )
    
    # Database settings
    DATABASE_URL: str = Field(default_factory=lambda: os.getenv("DATABASE_URL", "sqlite:///./ipdb.sqlite3"))
    TEST_DATABASE_URL: str = Field(default_factory=lambda: os.getenv("TEST_DATABASE_URL", "sqlite:///./test_ipdb.sqlite3"))
    
    # Security settings
    RATE_LIMIT_ENABLED: bool = Field(default_factory=lambda: os.getenv("RATE_LIMIT_ENABLED", "true").lower() == "true")
    RATE_LIMIT_DEFAULT: str = Field(default_factory=lambda: os.getenv("RATE_LIMIT_DEFAULT", "1000 per day, 100 per hour"))
    
    # Admin credentials
    ADMIN_USER: str = Field(default_factory=lambda: os.getenv("ADMIN_USER", "admin"))
    ADMIN_PASS_HASH: str = Field(default_factory=lambda: os.getenv("ADMIN_PASS_HASH", ""))
    
    # Logging settings
    LOG_LEVEL: str = Field(default_factory=lambda: os.getenv("LOG_LEVEL", "INFO"))
    LOG_FORMAT: str = Field(
        default_factory=lambda: os.getenv(
            "LOG_FORMAT",
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
    )
    
    # File upload settings
    MAX_UPLOAD_SIZE: int = Field(default_factory=lambda: int(os.getenv("MAX_UPLOAD_SIZE", "10485760")))  # 10MB
    ALLOWED_EXTENSIONS: List[str] = Field(
        default_factory=lambda: os.getenv(
            "ALLOWED_EXTENSIONS",
            "csv,txt,json"
        ).split(",")
    )
    
    # Email settings (if needed)
    SMTP_SERVER: str = Field(default_factory=lambda: os.getenv("SMTP_SERVER", "smtp.gmail.com"))
    SMTP_PORT: int = Field(default_factory=lambda: int(os.getenv("SMTP_PORT", "587")))
    SMTP_USER: str = Field(default_factory=lambda: os.getenv("SMTP_USER", ""))
    SMTP_PASSWORD: str = Field(default_factory=lambda: os.getenv("SMTP_PASSWORD", ""))
    
    class Config:
        env_file = ".env"
        env_file_encoding = 'utf-8'
        case_sensitive = True

# Create settings instance with environment variables
settings = Settings()

def get_settings() -> Settings:
    """Get application settings."""
    return settings
