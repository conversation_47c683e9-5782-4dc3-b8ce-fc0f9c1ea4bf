import sqlite3

# Connect to the database
conn = sqlite3.connect('ipdb.sqlite3')
cursor = conn.cursor()

# Check if the user exists
cursor.execute("SELECT username, email FROM users WHERE email='<EMAIL>'")
result = cursor.fetchone()
print('User found:', result if result else 'No user found')

# Count all users
cursor.execute("SELECT COUNT(*) FROM users")
print('Total users:', cursor.fetchone()[0])

# Count IP records
cursor.execute("SELECT COUNT(*) FROM ip_records")
print('Total IP records:', cursor.fetchone()[0])

# Close the connection
conn.close()