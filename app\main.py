"""Main FastAPI application module."""
import os
import logging
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse, HTMLResponse
from fastapi.middleware.gzip import GZipMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.middleware.cors import CORSMiddleware

from config import settings, get_settings
from database import init_db, check_database_connection
from endpoints import lookup, auth, admin, users
from security import (
    SECURITY_HEADERS,
    limiter,
    get_cors_middleware,
    SecurityMiddleware,
    validate_input,
    sanitize_input
)

# Configure logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
os.makedirs(log_dir, exist_ok=True)

# Main application logger
logger = logging.getLogger('app')
logger.setLevel(settings.LOG_LEVEL)

# Clear existing handlers
for handler in logger.handlers[:]:
    logger.removeHandler(handler)
    handler.close()

# File handler for logs
log_file = os.path.join(log_dir, 'app.log')
file_handler = logging.FileHandler(log_file, encoding='utf-8')
file_handler.setLevel(logging.DEBUG)
file_formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
file_handler.setFormatter(file_formatter)

# Console handler
console_handler = logging.StreamHandler()
console_handler.setLevel(settings.LOG_LEVEL)
console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', '%H:%M:%S')
console_handler.setFormatter(console_formatter)

# Add handlers
logger.addHandler(file_handler)
logger.addHandler(console_handler)

# Set log level for uvicorn and fastapi
logging.getLogger('uvicorn').setLevel(settings.LOG_LEVEL)
logging.getLogger('fastapi').setLevel(settings.LOG_LEVEL)

# Initialize FastAPI app
app = FastAPI(
    title=settings.APP_NAME,
    description="IP Lookup Service API",
    version="1.0.0",
    docs_url="/api/docs" if settings.DEBUG else None,
    redoc_url="/api/redoc" if settings.DEBUG else None,
    openapi_url="/api/openapi.json" if settings.DEBUG else None,
)

# Add security middleware
app.add_middleware(SecurityMiddleware)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["Content-Length", "X-Request-ID"],
    max_age=600,  # 10 minutes
)

# Add GZip middleware for compression
app.add_middleware(GZipMiddleware, minimum_size=1000)

# Initialize rate limiter with better error handling
try:
    if settings.RATE_LIMIT_ENABLED:
        logger.info("Rate limiting is enabled")
        from security import limit, get_rate_limiter
        
        # Get the limiter instance
        limiter = get_rate_limiter()
        
        # Apply rate limiting to all API routes
        for route in app.routes:
            if hasattr(route, 'endpoint') and hasattr(route, 'path'):
                try:
                    # Skip static files and other non-API routes
                    if route.path.startswith('/static'):
                        continue
                        
                    # Get the current endpoint
                    endpoint = route.endpoint
                    logger.debug(f"Applying rate limit to {route.path}")
                    
                    # Create a new endpoint with rate limiting
                    route.endpoint = limit(settings.RATE_LIMIT_DEFAULT)(endpoint)
                except Exception as e:
                    logger.error(f"Error applying rate limit to {getattr(route, 'path', 'unknown')}: {str(e)}")
                    continue
        
        logger.info("Rate limiting applied to routes")
except Exception as e:
    logger.error(f"Error initializing rate limiter: {str(e)}")
    if settings.DEBUG:
        logger.exception("Rate limiter initialization error:")

# Add rate limit exception handler
@app.exception_handler(Exception)
async def validation_exception_handler(request: Request, exc: Exception):
    if isinstance(exc, HTTPException):
        logger.warning(f"HTTPException: {exc.status_code} - {exc.detail}")
        return JSONResponse(
            status_code=exc.status_code,
            content={"detail": str(exc.detail)},
        )
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    logger.error(f"Request URL: {request.url}")
    logger.error(f"Request method: {request.method}")
    logger.error(f"Request headers: {dict(request.headers)}")
    return JSONResponse(
        status_code=500,
        content={"detail": f"Internal server error: {str(exc)}"},
    )

# Ensure DB and tables are initialized at startup
@app.on_event("startup")
async def on_startup():
    """Initialize application services."""
    try:
        logger.info("Starting application initialization...")
        
        # Initialize database
        try:
            from database import init_db
            logger.info("Initializing database...")
            await init_db()
            logger.info("Database initialization completed")
        except Exception as db_error:
            logger.critical(f"Database initialization failed: {str(db_error)}")
            if settings.DEBUG:
                logger.exception("Database error details:")
            raise RuntimeError(f"Database initialization failed: {str(db_error)}")
        
        # Verify database connection
        logger.info("Verifying database connection...")
        try:
            if not check_database_connection():
                error_msg = "Failed to connect to the database"
                logger.error(error_msg)
                raise RuntimeError(error_msg)
            logger.info("Database connection verified")
        except Exception as conn_error:
            logger.critical(f"Database connection test failed: {str(conn_error)}")
            if settings.DEBUG:
                logger.exception("Connection error details:")
            raise
        
        # Log all routes for debugging
        if settings.DEBUG:
            logger.info("Registered routes:")
            for route in app.routes:
                if hasattr(route, 'methods') and hasattr(route, 'path'):
                    logger.info(f"  {route.path}: {', '.join(route.methods) if route.methods else 'N/A'}")
        
        logger.info("Application startup completed successfully")
        
    except Exception as e:
        logger.critical(f"Startup failed: {str(e)}")
        if settings.DEBUG:
            logger.exception("Startup error details:")
        raise


# Mount static files
static_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "static"))
print(f"Configuring static files from: {static_dir}")
logger.info(f"Configuring static files from: {static_dir}")

# Ensure the static directory exists
if not os.path.exists(static_dir):
    logger.warning(f"Static directory not found: {static_dir}")
    os.makedirs(static_dir, exist_ok=True)
    logger.info(f"Created static directory: {static_dir}")

# Mount static files with html=True to properly serve HTML files
app.mount("/static", StaticFiles(directory=static_dir, html=True), name="static")
logger.info(f"Static files mounted at /static")

logger.info(f"Static files mounted at /static")

# Include routers with rate limiting
app.include_router(lookup.router, prefix="/api/v1", tags=["lookup"])
app.include_router(auth.router, prefix="/api/v1/auth", tags=["auth"])
app.include_router(admin.router, prefix="/api/v1/admin", tags=["admin"])
app.include_router(users.router, prefix="/api/v1", tags=["users"])

# Root endpoint

@app.get("/")
@app.get("/index.html")
async def root():
    """Serve the main application page."""
    try:
        # Use absolute path for better reliability
        file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "static", "index.html"))
        logger.info(f"Serving index.html from: {file_path}")
        
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            # Return a basic HTML page with error message instead of raising an exception
            return HTMLResponse(content="<html><body><h1>Error: Index file not found</h1><p>Please make sure static/index.html exists.</p></body></html>")
            
        # Ensure proper content type is set
        return FileResponse(
            path=file_path, 
            media_type="text/html"
        )
    except Exception as e:
        logger.error(f"Error serving index.html: {str(e)}", exc_info=True)
        return HTMLResponse(content=f"<html><body><h1>Server Error</h1><p>{str(e)}</p></body></html>", status_code=500)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring."""
    return {"status": "ok", "environment": settings.ENVIRONMENT}

# Add direct routes for login and signup pages
@app.get("/login")
@app.get("/login.html")
async def login_page():
    """Serve the login page."""
    file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "static", "login.html"))
    if not os.path.exists(file_path):
        logger.error(f"File not found: {file_path}")
        return HTMLResponse(content="<html><body><h1>Error: Login file not found</h1><p>Please make sure static/login.html exists.</p></body></html>")
    return FileResponse(path=file_path, media_type="text/html")

@app.get("/signup")
@app.get("/signup.html")
async def signup_page():
    """Serve the signup page."""
    file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "static", "signup.html"))
    if not os.path.exists(file_path):
        logger.error(f"File not found: {file_path}")
        return HTMLResponse(content="<html><body><h1>Error: Signup file not found</h1><p>Please make sure static/signup.html exists.</p></body></html>")
    return FileResponse(path=file_path, media_type="text/html")

@app.get("/admin")
@app.get("/admin.html")
async def admin_page():
    """Serve the admin dashboard page."""
    file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "static", "admin.html"))
    if not os.path.exists(file_path):
        logger.error(f"File not found: {file_path}")
        return HTMLResponse(content="<html><body><h1>Error: Admin dashboard file not found</h1><p>Please make sure static/admin.html exists.</p></body></html>")
    return FileResponse(path=file_path, media_type="text/html")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring."""
    return {"status": "ok", "environment": settings.ENVIRONMENT}

# Rate limiting is already applied to all routes

# Add rate limiting to specific endpoints
@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    """Add security headers to all responses."""
    response = await call_next(request)
    
    # Add security headers
    for header, value in SECURITY_HEADERS.items():
        response.headers[header] = value
    
    return response
