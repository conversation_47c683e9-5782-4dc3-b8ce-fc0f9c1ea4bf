from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel
from sqlmodel import select
from models import IPRecord
from database import get_session
import ipaddress

router = APIRouter()

class LookupRequest(BaseModel):
    query: str

@router.post("/lookup/")
def lookup_ip(request: LookupRequest, session=Depends(get_session)):
    try:
        # Parse input
        if "/" in request.query:
            net = ipaddress.ip_network(request.query, strict=False)
            ip_start = int(net.network_address)
            ip_end = int(net.broadcast_address)
            ip_version = net.version
        else:
            ip = ipaddress.ip_address(request.query)
            ip_start = ip_end = int(ip)
            ip_version = ip.version
    except Exception:
        raise HTTPException(status_code=400, detail=f"Invalid IP/CIDR format: '{request.query}'")
    # Query DB for overlapping ranges
    results = session.exec(
        select(IPRecord).where(IPRecord.ip_start <= ip_end, IPRecord.ip_end >= ip_start, IPRecord.ip_version == ip_version)
    ).all()
    if not results:
        raise HTTPException(status_code=404, detail=f"No data found for {request.query}")
    return [r.dict() for r in results]
