import sqlite3

conn = sqlite3.connect('ipdb.sqlite3')
cursor = conn.cursor()

# Check tables
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()
print("Tables in database:")
print(tables)

# Check if ip_records table exists
if ('ip_records',) in tables:
    cursor.execute("SELECT COUNT(*) FROM ip_records")
    count = cursor.fetchone()[0]
    print(f"Number of records in ip_records: {count}")

# Check if users table exists
if ('users',) in tables:
    cursor.execute("SELECT COUNT(*) FROM users")
    count = cursor.fetchone()[0]
    print(f"Number of records in users: {count}")

conn.close()