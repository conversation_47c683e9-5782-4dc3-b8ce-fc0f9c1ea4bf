"""Database configuration."""
import os
from sqlmodel import create_engine
from sqlalchemy.pool import StaticPool
import logging

# Get logger
logger = logging.getLogger('app.db_config')

# Get database URL from environment or use default
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./ipdb.sqlite3")

# Configure engine based on database type
if "sqlite" in DATABASE_URL:
    logger.info("Configuring SQLite database...")
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,  # Use StaticPool for SQLite
        echo=os.getenv('SQL_ECHO', 'false').lower() == 'true'
    )
else:
    # For other databases (PostgreSQL, MySQL, etc.)
    logger.info(f"Configuring database: {DATABASE_URL}")
    engine = create_engine(
        DATABASE_URL,
        pool_size=int(os.getenv('DB_POOL_SIZE', '5')),
        max_overflow=int(os.getenv('DB_MAX_OVERFLOW', '10')),
        pool_pre_ping=True,
        echo=os.getenv('SQL_ECHO', 'false').lower() == 'true'
    )

logger.info(f"Database engine configured for: {DATABASE_URL}")
