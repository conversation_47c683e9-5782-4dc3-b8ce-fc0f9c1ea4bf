// main.js - <PERSON>les frontend logic for all pages

// API base URL
const API_BASE_URL = '/api/v1';

// Token management
const TOKEN_KEY = 'access_token';
const REFRESH_TOKEN_KEY = 'refresh_token';

// Toast notification utility - moved to line 156

// IP Lookup Page
const lookupForm = document.getElementById('lookup-form');
if (lookupForm) {
  lookupForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    const query = document.getElementById('ip-query').value;
    const resultDiv = document.getElementById('result');
    resultDiv.textContent = 'Looking up...';
    
    try {
      const data = await apiRequest('/lookup/', {
        method: 'POST',
        body: { query }
      });
      resultDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
    } catch (error) {
      resultDiv.textContent = error.message || 'Lookup failed. Please try again.';
    }
  });
}

// API request wrapper with token refresh
async function apiRequest(url, options = {}) {
  // Set up default headers
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers
  };

  // Add auth token if available
  const token = localStorage.getItem(TOKEN_KEY);
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  try {
    const response = await fetch(`${API_BASE_URL}${url}`, {
      ...options,
      headers,
      body: options.body ? JSON.stringify(options.body) : null
    });

    // If token is expired, try to refresh it
    if (response.status === 401 && url !== '/auth/refresh/') {
      const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);
      if (refreshToken) {
        try {
          const refreshResponse = await fetch(`${API_BASE_URL}/auth/refresh/`, {
            method: 'POST',
            headers: { 'Authorization': `Bearer ${refreshToken}` }
          });
          
          if (refreshResponse.ok) {
            const { access_token } = await refreshResponse.json();
            localStorage.setItem(TOKEN_KEY, access_token);
            // Retry the original request with the new token
            headers['Authorization'] = `Bearer ${access_token}`;
            const retryResponse = await fetch(`${API_BASE_URL}${url}`, {
              ...options,
              headers,
              body: options.body ? JSON.stringify(options.body) : null
            });
            return await retryResponse.json();
          } else {
            // If refresh fails, redirect to login
            redirectToLogin();
            throw new Error('Session expired. Please log in again.');
          }
        } catch (error) {
          redirectToLogin();
          throw error;
        }
      } else {
        redirectToLogin();
        throw new Error('Session expired. Please log in again.');
      }
    }

    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.detail || 'Request failed');
    }

    return await response.json();
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
}

// Helper function to redirect to login
function redirectToLogin() {
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem(REFRESH_TOKEN_KEY);
  if (!window.location.pathname.includes('login.html')) {
    window.location.href = '/static/login.html';
  }
}

// Login Page
const loginForm = document.getElementById('login-form');
if (loginForm) {
  loginForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const loginResult = document.getElementById('login-result');
    loginResult.textContent = 'Logging in...';
    
    try {
      const data = await apiRequest('/auth/login/', {
        method: 'POST',
        body: { username, password }
      });

      if (data.access_token) {
        localStorage.setItem(TOKEN_KEY, data.access_token);
        if (data.refresh_token) {
          localStorage.setItem(REFRESH_TOKEN_KEY, data.refresh_token);
        }
        showToast('Login successful!');
        setTimeout(() => { window.location.href = '/static/admin.html'; }, 1000);
      } else {
        loginResult.textContent = 'Invalid response from server';
      }
    } catch (error) {
      loginResult.textContent = error.message || 'Login failed. Please try again.';
    }
  });
}

// Toast Notification System
const toastElement = document.getElementById('toast-content');
const toastMessage = document.getElementById('toast-message');
const toastClose = document.getElementById('toast-close');
let toastTimeout;

function showToast(message, type = 'success') {
  if (!toastElement || !toastMessage) return;
  
  // Clear any existing timeout
  if (toastTimeout) {
    clearTimeout(toastTimeout);
  }
  
  // Set message and type
  toastMessage.textContent = message;
  
  // Update styles based on type
  toastElement.className = 'px-6 py-3 rounded-lg shadow-lg transform transition-all duration-300 ease-in-out';
  
  switch (type) {
    case 'error':
      toastElement.classList.add('bg-red-600', 'text-white');
      break;
    case 'warning':
      toastElement.classList.add('bg-yellow-500', 'text-white');
      break;
    case 'success':
    default:
      toastElement.classList.add('bg-green-600', 'text-white');
      break;
  }
  
  // Show toast
  toastElement.classList.remove('translate-y-16', 'opacity-0');
  toastElement.classList.add('translate-y-0', 'opacity-100');
  
  // Auto-hide after delay
  toastTimeout = setTimeout(() => {
    hideToast();
  }, 5000);
}

function hideToast() {
  if (!toastElement) return;
  toastElement.classList.remove('translate-y-0', 'opacity-100');
  toastElement.classList.add('translate-y-16', 'opacity-0');
}

// Close toast when close button is clicked
if (toastClose) {
  toastClose.addEventListener('click', hideToast);
}

// Modal System
function showModal(modalId) {
  const modal = document.getElementById(modalId);
  if (modal) {
    modal.classList.remove('hidden');
    document.body.style.overflow = 'hidden';
  }
}

function hideModal(modalId) {
  const modal = document.getElementById(modalId);
  if (modal) {
    modal.classList.add('hidden');
    document.body.style.overflow = '';
  }
}

// Close modals when clicking outside
window.addEventListener('click', (e) => {
  if (e.target.classList.contains('fixed') && e.target.id.endsWith('-modal')) {
    hideModal(e.target.id);
  }
});

// Tab System
function setupTabs() {
  const tabs = document.querySelectorAll('[id$="-tab"]');
  const tabContents = document.querySelectorAll('[id$="-management"], #settings');
  
  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      // Hide all tab contents
      tabContents.forEach(content => content.classList.add('hidden'));
      
      // Remove active class from all tabs
      tabs.forEach(t => {
        t.classList.remove('border-blue-500', 'text-blue-400');
        t.classList.add('border-transparent', 'text-gray-400', 'hover:text-gray-300', 'hover:border-gray-300');
      });
      
      // Show selected content and update tab style
      const targetId = tab.id.replace('-tab', '');
      const targetContent = document.getElementById(targetId);
      if (targetContent) {
        targetContent.classList.remove('hidden');
        tab.classList.remove('border-transparent', 'text-gray-400', 'hover:text-gray-300', 'hover:border-gray-300');
        tab.classList.add('border-blue-500', 'text-blue-400');
      }
    });
  });
  
  // Activate first tab by default
  if (tabs.length > 0) tabs[0].click();
}

// Admin Dashboard - IP Management
const exportBtn = document.getElementById('export-btn');
if (exportBtn) {
  exportBtn.addEventListener('click', async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/admin/export/`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem(TOKEN_KEY)}` }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'ip_records.csv';
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
        showToast('Exported IP data successfully!');
      } else {
        const error = await response.json().catch(() => ({}));
        throw new Error(error.detail || 'Export failed');
      }
    } catch (error) {
      showToast(error.message || 'Error exporting data', 'error');
    }
  });
}

const uploadBtn = document.getElementById('upload-btn');
const uploadFile = document.getElementById('upload-file');
if (uploadBtn && uploadFile) {
  uploadBtn.addEventListener('click', () => uploadFile.click());
  uploadFile.addEventListener('change', async () => {
    const file = uploadFile.files[0];
    if (!file) return;
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await apiRequest('/admin/upload/', {
        method: 'POST',
        body: formData
      });
      
      showToast('File uploaded and processed successfully!');
      // Refresh IP table after successful upload
      loadIpTable();
    } catch (error) {
      showToast(error.message || 'Error uploading file', 'error');
    } finally {
      uploadFile.value = ''; // Reset file input
    }
  });
}

// IP Table Management
async function loadIpTable(page = 1, pageSize = 10, filters = {}) {
  const tbody = document.getElementById('ip-table-body');
  if (!tbody) return;
  
  try {
    tbody.innerHTML = '<tr><td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-400 text-center">Loading IP records...</td></tr>';
    
    // Build query string from filters
    const query = new URLSearchParams({
      page,
      page_size: pageSize,
      ...filters
    });
    
    const data = await apiRequest(`/admin/ips/?${query.toString()}`);
    
    if (data.results && data.results.length > 0) {
      tbody.innerHTML = data.results.map(ip => `
        <tr class="hover:bg-gray-700">
          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-300">${ip.ip_address}${ip.cidr ? `/${ip.cidr}` : ''}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">${ip.country || 'N/A'}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">${ip.owner || 'N/A'}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">${new Date(ip.updated_at).toLocaleString()}</td>
          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
            <button class="text-blue-500 hover:text-blue-400 mr-3 edit-ip" data-id="${ip.id}">Edit</button>
            <button class="text-red-500 hover:text-red-400 delete-ip" data-id="${ip.id}">Delete</button>
          </td>
        </tr>
      `).join('');
      
      // Update pagination
      updatePagination('ip', data.count, page, pageSize);
      
      // Attach event listeners to action buttons
      attachIpRowEventListeners();
    } else {
      tbody.innerHTML = '<tr><td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-400 text-center">No IP records found</td></tr>';
    }
  } catch (error) {
    console.error('Error loading IP table:', error);
    tbody.innerHTML = '<tr><td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-red-400 text-center">Error loading IP records</td></tr>';
  }
}

function attachIpRowEventListeners() {
  // Edit IP
  document.querySelectorAll('.edit-ip').forEach(btn => {
    btn.addEventListener('click', (e) => {
      const ipId = e.target.dataset.id;
      // TODO: Implement edit IP functionality
      showToast('Edit IP functionality coming soon', 'warning');
    });
  });
  
  // Delete IP
  document.querySelectorAll('.delete-ip').forEach(btn => {
    btn.addEventListener('click', (e) => {
      const ipId = e.target.dataset.id;
      showConfirmModal(
        'Delete IP',
        'Are you sure you want to delete this IP record? This action cannot be undone.',
        async () => {
          try {
            await apiRequest(`/admin/ips/${ipId}/`, { method: 'DELETE' });
            showToast('IP record deleted successfully');
            loadIpTable(); // Refresh the table
          } catch (error) {
            showToast(error.message || 'Failed to delete IP record', 'error');
          }
        }
      );
    });
  });
}

// User Management
const addUserBtn = document.getElementById('add-user-btn');
const userForm = document.getElementById('user-form');
const saveUserBtn = document.getElementById('save-user-btn');
const cancelUserBtn = document.getElementById('cancel-user-btn');

if (addUserBtn) {
  addUserBtn.addEventListener('click', () => {
    // Reset form
    if (userForm) userForm.reset();
    // Show user modal
    showModal('user-modal');
  });
}

if (cancelUserBtn) {
  cancelUserBtn.addEventListener('click', () => hideModal('user-modal'));
}

if (saveUserBtn && userForm) {
  saveUserBtn.addEventListener('click', async () => {
    const formData = new FormData(userForm);
    const userId = document.getElementById('user-id').value;
    const isEdit = !!userId;
    
    try {
      // Basic form validation
      if (!formData.get('username') || !formData.get('email')) {
        throw new Error('Username and email are required');
      }
      
      const password = formData.get('password');
      if (!isEdit && !password) {
        throw new Error('Password is required for new users');
      }
      
      if (password && password !== formData.get('confirm_password')) {
        throw new Error('Passwords do not match');
      }
      
      // Prepare user data
      const userData = {
        username: formData.get('username'),
        email: formData.get('email'),
        role: formData.get('role'),
        is_active: formData.get('is_active') === 'on'
      };
      
      if (password) {
        userData.password = password;
      }
      
      // Make API request
      const url = isEdit ? `/admin/users/${userId}/` : '/admin/users/';
      const method = isEdit ? 'PUT' : 'POST';
      
      await apiRequest(url, {
        method,
        body: JSON.stringify(userData)
      });
      
      showToast(`User ${isEdit ? 'updated' : 'created'} successfully`);
      hideModal('user-modal');
      loadUserTable(); // Refresh user table
    } catch (error) {
      showToast(error.message || 'Error saving user', 'error');
    }
  });
}

// Load users table
async function loadUserTable(page = 1, pageSize = 10) {
  const tbody = document.getElementById('user-table-body');
  if (!tbody) return;
  
  try {
    tbody.innerHTML = '<tr><td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-gray-400 text-center">Loading user data...</td></tr>';
    
    const data = await apiRequest(`/admin/users/?page=${page}&page_size=${pageSize}`);
    
    if (data.results && data.results.length > 0) {
      tbody.innerHTML = data.results.map(user => `
        <tr class="hover:bg-gray-700">
          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-300">${user.username}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">${user.email}</td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.role === 'admin' ? 'bg-purple-600 text-purple-100' : 'bg-blue-600 text-blue-100'}">
              ${user.role}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.is_active ? 'bg-green-600 text-green-100' : 'bg-red-600 text-red-100'}">
              ${user.is_active ? 'Active' : 'Inactive'}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
            ${user.last_login ? new Date(user.last_login).toLocaleString() : 'Never'}
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
            <button class="text-blue-500 hover:text-blue-400 mr-3 edit-user" data-user='${JSON.stringify(user)}'>Edit</button>
            <button class="text-red-500 hover:text-red-400 delete-user" data-id="${user.id}" ${user.role === 'admin' ? 'disabled' : ''}>Delete</button>
          </td>
        </tr>
      `).join('');
      
      // Update pagination
      updatePagination('user', data.count, page, pageSize);
      
      // Attach event listeners to action buttons
      attachUserRowEventListeners();
    } else {
      tbody.innerHTML = '<tr><td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-gray-400 text-center">No users found</td></tr>';
    }
  } catch (error) {
    console.error('Error loading user table:', error);
    tbody.innerHTML = '<tr><td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-red-400 text-center">Error loading user data</td></tr>';
  }
}

function attachUserRowEventListeners() {
  // Edit User
  document.querySelectorAll('.edit-user').forEach(btn => {
    btn.addEventListener('click', (e) => {
      const userData = JSON.parse(e.target.dataset.user);
      populateUserForm(userData);
      showModal('user-modal');
    });
  });
  
  // Delete User
  document.querySelectorAll('.delete-user').forEach(btn => {
    btn.addEventListener('click', (e) => {
      if (e.target.disabled) return;
      
      const userId = e.target.dataset.id;
      showConfirmModal(
        'Delete User',
        'Are you sure you want to delete this user? This action cannot be undone.',
        async () => {
          try {
            await apiRequest(`/admin/users/${userId}/`, { method: 'DELETE' });
            showToast('User deleted successfully');
            loadUserTable(); // Refresh the table
          } catch (error) {
            showToast(error.message || 'Failed to delete user', 'error');
          }
        }
      );
    });
  });
}

function populateUserForm(userData) {
  if (!userData) return;
  
  document.getElementById('user-id').value = userData.id || '';
  document.getElementById('username').value = userData.username || '';
  document.getElementById('email').value = userData.email || '';
  document.getElementById('role').value = userData.role || 'user';
  document.getElementById('is-active').checked = userData.is_active !== false;
  
  // Clear password fields
  document.getElementById('password').value = '';
  document.getElementById('confirm-password').value = '';
  
  // Update modal title
  const modalTitle = document.getElementById('modal-title');
  if (modalTitle) {
    modalTitle.textContent = `Edit User: ${userData.username}`;
  }
}

// Confirmation Modal
function showConfirmModal(title, message, confirmCallback) {
  const titleEl = document.getElementById('confirm-modal-title');
  const messageEl = document.getElementById('confirm-modal-message');
  const confirmBtn = document.getElementById('confirm-action-btn');
  
  if (titleEl) titleEl.textContent = title;
  if (messageEl) messageEl.textContent = message;
  
  // Remove previous event listeners
  const newConfirmBtn = confirmBtn.cloneNode(true);
  confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
  
  // Add new event listener
  newConfirmBtn.addEventListener('click', () => {
    hideModal('confirm-modal');
    if (typeof confirmCallback === 'function') {
      confirmCallback();
    }
  });
  
  showModal('confirm-modal');
}

// Initialize the page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Setup tabs
  setupTabs();
  
  // Load initial data
  loadIpTable();
  loadUserTable();
  
  // Setup filter form
  const filterForm = document.getElementById('filter-form');
  if (filterForm) {
    filterForm.addEventListener('submit', (e) => {
      e.preventDefault();
      const filters = {
        ip: document.getElementById('filter-ip').value,
        country: document.getElementById('filter-country').value,
        owner: document.getElementById('filter-owner').value
      };
      loadIpTable(1, 10, filters);
    });
    
    // Reset button
    const resetBtn = filterForm.querySelector('button[type="reset"]');
    if (resetBtn) {
      resetBtn.addEventListener('click', () => {
        setTimeout(() => loadIpTable(), 0);
      });
    }
  }
  
  // Setup pagination
  function setupPagination(type) {
    const prevBtn = document.getElementById(`${type}-prev-page`);
    const nextBtn = document.getElementById(`${type}-next-page`);
    const pageLinks = document.querySelectorAll(`[data-${type}-page]`);
    
    if (prevBtn) {
      prevBtn.addEventListener('click', (e) => {
        e.preventDefault();
        const currentPage = parseInt(document.getElementById(`${type}-current-page`).textContent, 10);
        if (currentPage > 1) {
          if (type === 'ip') {
            loadIpTable(currentPage - 1);
          } else {
            loadUserTable(currentPage - 1);
          }
        }
      });
    }
    
    if (nextBtn) {
      nextBtn.addEventListener('click', (e) => {
        e.preventDefault();
        const currentPage = parseInt(document.getElementById(`${type}-current-page`).textContent, 10);
        const totalPages = Math.ceil(parseInt(document.getElementById(`${type}-total-items`).textContent, 10) / 10);
        if (currentPage < totalPages) {
          if (type === 'ip') {
            loadIpTable(currentPage + 1);
          } else {
            loadUserTable(currentPage + 1);
          }
        }
      });
    }
    
    pageLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const page = parseInt(link.dataset[`${type}Page`], 10);
        if (type === 'ip') {
          loadIpTable(page);
        } else {
          loadUserTable(page);
        }
      });
    });
  }
  
  setupPagination('ip');
  setupPagination('user');
  
  // Handle settings form submission
  const settingsForm = document.getElementById('settings-form');
  if (settingsForm) {
    settingsForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      const formData = new FormData(settingsForm);
      const settings = {};
      
      // Convert form data to object
      formData.forEach((value, key) => {
        settings[key] = value === 'on' ? true : value;
      });
      
      try {
        await apiRequest('/admin/settings/', {
          method: 'POST',
          body: JSON.stringify(settings)
        });
        showToast('Settings saved successfully');
      } catch (error) {
        showToast(error.message || 'Error saving settings', 'error');
      }
    });
  }
  
  // Load settings
  async function loadSettings() {
    try {
      const settings = await apiRequest('/admin/settings/');
      
      // Update form fields
      if (settings.enable_registration !== undefined) {
        document.getElementById('enable-registration').checked = settings.enable_registration;
      }
      if (settings.email_verification !== undefined) {
        document.getElementById('email-verification').checked = settings.email_verification;
      }
      if (settings.session_timeout !== undefined) {
        document.getElementById('session-timeout').value = settings.session_timeout;
      }
      if (settings.enable_2fa !== undefined) {
        document.getElementById('enable-2fa').checked = settings.enable_2fa;
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  }
  
  loadSettings();
});

// Helper function to update pagination UI
function updatePagination(type, totalItems, currentPage, pageSize) {
  const totalPages = Math.ceil(totalItems / pageSize);
  const startItem = ((currentPage - 1) * pageSize) + 1;
  const endItem = Math.min(currentPage * pageSize, totalItems);
  
  // Update pagination info
  const paginationInfo = document.getElementById(`${type}-pagination-info`);
  if (paginationInfo) {
    paginationInfo.textContent = `Showing ${startItem} to ${endItem} of ${totalItems} items`;
  }
  
  // Update current page indicator
  const currentPageEl = document.getElementById(`${type}-current-page`);
  if (currentPageEl) {
    currentPageEl.textContent = currentPage;
  }
  
  // Update total items
  const totalItemsEl = document.getElementById(`${type}-total-items`);
  if (totalItemsEl) {
    totalItemsEl.textContent = totalItems;
  }
  
  // Update pagination controls
  const prevBtn = document.getElementById(`${type}-prev-page`);
  const nextBtn = document.getElementById(`${type}-next-page`);
  
  if (prevBtn) {
    prevBtn.classList.toggle('opacity-50', currentPage === 1);
    prevBtn.disabled = currentPage === 1;
  }
  
  if (nextBtn) {
    nextBtn.classList.toggle('opacity-50', currentPage === totalPages);
    nextBtn.disabled = currentPage === totalPages;
  }
  
  // Update page numbers
  const paginationContainer = document.getElementById(`${type}-pagination`);
  if (paginationContainer) {
    let paginationHTML = '';
    
    // Always show first page
    paginationHTML += `
      <a href="#" data-${type}-page="1" class="relative inline-flex items-center px-4 py-2 border border-gray-700 text-sm font-medium ${currentPage === 1 ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}">
        1
      </a>
    `;
    
    // Show ellipsis if needed
    if (currentPage > 3) {
      paginationHTML += `
        <span class="relative inline-flex items-center px-4 py-2 border border-gray-700 bg-gray-800 text-sm font-medium text-gray-700">
          ...
        </span>
      `;
    }
    
    // Show current page and adjacent pages
    for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {
      paginationHTML += `
        <a href="#" data-${type}-page="${i}" class="relative inline-flex items-center px-4 py-2 border border-gray-700 text-sm font-medium ${currentPage === i ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}">
          ${i}
        </a>
      `;
    }
    
    // Show last page if not already shown
    if (totalPages > 1) {
      if (currentPage < totalPages - 1) {
        paginationHTML += `
          <span class="relative inline-flex items-center px-4 py-2 border border-gray-700 bg-gray-800 text-sm font-medium text-gray-700">
            ...
          </span>
        `;
      }
      
      paginationHTML += `
        <a href="#" data-${type}-page="${totalPages}" class="relative inline-flex items-center px-4 py-2 border border-gray-700 text-sm font-medium ${currentPage === totalPages ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}">
          ${totalPages}
        </a>
      `;
    }
    
    paginationContainer.innerHTML = paginationHTML;
    
    // Re-attach event listeners to new pagination links
    const pageLinks = document.querySelectorAll(`[data-${type}-page]`);
    pageLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const page = parseInt(link.dataset[`${type}Page`], 10);
        if (type === 'ip') {
          loadIpTable(page);
        } else {
          loadUserTable(page);
        }
      });
    });
  }
}

const logoutBtn = document.getElementById('logout-btn');
if (logoutBtn) {
  logoutBtn.addEventListener('click', () => {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
    showToast('Logged out.');
    setTimeout(() => { window.location.href = '/static/login.html'; }, 1000);
  });
}
