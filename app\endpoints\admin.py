from fastapi import APIRouter, HTTPException, UploadFile, File, Depends, Response
from sqlmodel import select
from models import IPRecord
from database import get_session
from jose import jwt, JWTError
from typing import Optional
import csv
import io
import os

SECRET_KEY = os.environ.get("JWT_SECRET", "secret-key")
ALGORITHM = "HS256"

router = APIRouter()

def get_current_user(token: Optional[str] = Depends(lambda: None)):
    # Dummy extraction from Authorization header for demo
    # In real app, use OAuth2PasswordBearer
    if not token:
        raise HTTPException(status_code=401, detail="Not authenticated")
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload.get("sub")
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

@router.get("/export/")
def export_ips(response: Response, session=Depends(get_session), token: Optional[str] = Depends(lambda: None)):
    get_current_user(token)
    records = session.exec(select(IPRecord)).all()
    output = io.StringIO()
    writer = csv.writer(output)
    writer.writerow(["ip_cidr", "ip_version", "country", "owner", "added_at", "comment"])
    for r in records:
        writer.writerow([r.ip_cidr, r.ip_version, r.country, r.owner, r.added_at, r.comment or ""])
    response.headers["Content-Disposition"] = "attachment; filename=ip_records.csv"
    response.headers["Content-Type"] = "text/csv"
    return Response(content=output.getvalue(), media_type="text/csv")

@router.post("/upload/")
def upload_ips(file: UploadFile = File(...), session=Depends(get_session), token: Optional[str] = Depends(lambda: None)):
    get_current_user(token)
    content = file.file.read().decode("utf-8")
    reader = csv.DictReader(io.StringIO(content))
    for row in reader:
        record = IPRecord(
            ip_cidr=row["ip_cidr"],
            ip_version=int(row["ip_version"]),
            country=row["country"],
            owner=row["owner"],
            comment=row.get("comment", "")
        )
        session.add(record)
    session.commit()
    return {"detail": "Upload complete"}

@router.post("/query/")
def query_ips(country: Optional[str] = None, owner: Optional[str] = None, session=Depends(get_session), token: Optional[str] = Depends(lambda: None)):
    get_current_user(token)
    q = select(IPRecord)
    if country:
        q = q.where(IPRecord.country == country)
    if owner:
        q = q.where(IPRecord.owner == owner)
    results = session.exec(q).all()
    return [r.dict() for r in results]
